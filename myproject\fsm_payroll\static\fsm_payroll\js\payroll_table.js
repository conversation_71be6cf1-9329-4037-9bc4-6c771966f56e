// Payroll Table Manager
const PayrollTableManager = {
    // Store payroll data
    payrollData: {},

    /**
     * Initializes the payroll table manager.
     */
    init() {
        this.loadInitialData();
        this.generateTable();
    },

    /**
     * Loads initial payroll data from the DOM.
     */
    loadInitialData() {
        try {
            const payrollDataElement = document.getElementById('payroll-data');
            
            if (payrollDataElement && payrollDataElement.textContent) {
                this.payrollData = JSON.parse(payrollDataElement.textContent);
            } else {
                this.payrollData = {
                    technicians: [],
                    dates: [],
                    data: {},
                    technician_summaries: {}
                };
            }
        } catch (error) {
            this.showError();
            this.payrollData = {
                technicians: [],
                dates: [],
                data: {},
                technician_summaries: {}
            };
        }
    },

    /**
     * Generates the complete payroll table.
     */
    generateTable() {
        const tbody = document.getElementById('payroll-table-body');
        
        if (!tbody) {
            return;
        }

        // Clear existing content
        tbody.innerHTML = '';

        // Check if we have data
        if (!this.payrollData.technicians || this.payrollData.technicians.length === 0) {
            this.showEmptyState(true);
            return;
        }

        this.showEmptyState(false);

        // Update table headers
        this.updateTableHeader(this.payrollData.dates);

        // Generate rows for each technician
        this.payrollData.technicians.forEach((technician) => {
            if (technician && technician.trim() !== '') {
                const technicianData = this.payrollData.data[technician] || {};
                const summaryData = this.payrollData.technician_summaries[technician];
                
                const row = this.createTableRow(
                    technician, 
                    technicianData, 
                    this.payrollData.dates, 
                    summaryData
                );
                
                tbody.appendChild(row);
            }
        });
    },

    /**
     * Updates the table header row to display the correct date columns.
     */
    updateTableHeader(dates) {
        const thead = document.querySelector('table thead');
        if (!thead) return;

        thead.innerHTML = '';
        const tr = document.createElement('tr');
        tr.className = 'bg-gray-50';

        // Technician column header
        const technicianTh = document.createElement('th');
        technicianTh.className = 'sticky top-0 left-0 z-30 bg-gray-50 border-b border-r px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[280px] min-w-[280px] max-w-[280px]';
        technicianTh.textContent = 'Technician';
        tr.appendChild(technicianTh);

        // Date headers
        dates.forEach(date => {
            const th = document.createElement('th');
            th.className = 'sticky top-0 z-20 bg-gray-50 border-b border-r px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[120px] min-w-[120px] max-w-[120px] md:w-[100px] md:min-w-[100px] md:max-w-[100px]';
            const dateObj = new Date(date);
            
            const dayName = dateObj.toLocaleDateString('en-US', { weekday: 'short' });
            const dayNumber = dateObj.getDate();
            const monthName = dateObj.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
            
            th.innerHTML = `
                <div class="flex flex-col items-center">
                    <span class="text-gray-400 text-xs font-normal">${dayName}</span>
                    <span class="text-gray-600">${dayNumber} ${monthName}</span>
                </div>
            `;
            tr.appendChild(th);
        });

        // Summary header
        const summaryTh = document.createElement('th');
        summaryTh.className = 'sticky top-0 right-0 z-30 bg-gray-50 border-b border-l px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[200px] min-w-[200px]';
        summaryTh.textContent = 'Summary';
        tr.appendChild(summaryTh);

        thead.appendChild(tr);
    },

    /**
     * Creates a table row for a technician.
     */
    createTableRow(technician, data, dates, summaryData) {
        const tr = document.createElement('tr');
        tr.className = 'relative';

        // Technician name cell
        const technicianCell = this.createTechnicianNameCell(technician);
        tr.appendChild(technicianCell);

        // Date cells
        dates.forEach(date => {
            const dayData = data[date];
            const dateCell = this.createDateCell(technician, date, dayData);
            tr.appendChild(dateCell);
        });

        // Summary cell
        const summaryCell = this.createSummaryCell(technician, summaryData);
        tr.appendChild(summaryCell);

        return tr;
    },

    /**
     * Creates the technician name cell.
     */
    createTechnicianNameCell(technician) {
        const td = document.createElement('td');
        td.className = 'sticky left-0 z-10 bg-white border-b border-r px-3 py-2 text-xs font-medium text-gray-900 w-[280px] min-w-[280px] max-w-[280px]';
        td.style.wordBreak = 'break-all';
        td.style.lineHeight = '1.3';
        td.textContent = technician;
        td.title = technician;
        return td;
    },

    /**
     * Creates a date cell for a specific technician and date.
     */
    createDateCell(technician, date, dayData) {
        const td = document.createElement('td');
        const baseClasses = 'border-b border-r px-2 py-2 text-sm whitespace-nowrap relative w-[120px] min-w-[120px] max-w-[120px] md:w-[100px] md:min-w-[100px] md:max-w-[100px] overflow-hidden';
        
        if (!dayData || dayData.total_hours === 0) {
            td.className = `${baseClasses} bg-gray-50 text-gray-400 text-center cursor-pointer`;
            td.textContent = '-';
            return td;
        }

        // Determine cell status and styling
        const status = this.determineCellStatus(dayData);
        const statusClass = this.getStatusClass(status);
        
        td.className = `${baseClasses} ${statusClass} text-center cursor-pointer transition-all duration-150`;
        td.textContent = `${this.formatHours(dayData.total_hours)}h`;

        return td;
    },

    /**
     * Creates the summary cell.
     */
    createSummaryCell(technician, summaryData) {
        const td = document.createElement('td');
        td.className = 'sticky right-0 z-10 bg-white border-b border-l px-3 py-4 text-sm w-[200px] min-w-[200px] align-top';
        
        if (!summaryData) {
            td.innerHTML = '<span class="text-gray-400">No data</span>';
            return td;
        }

        // Use backend field names with fallbacks
        const totalHours = summaryData.total_hours_period || summaryData.total_hours || 0;
        const approvedHours = summaryData.total_approved_period || summaryData.approved_hours || 0;
        const pendingHours = summaryData.total_pending_period || summaryData.pending_hours || 0;
        const rejectedHours = summaryData.total_rejected_period || summaryData.rejected_hours || 0;
        const totalUnits = summaryData.total_units_period || 0;
        const workingDays = summaryData.days_worked || 0;

        td.innerHTML = `
            <div class="space-y-1 text-xs">
                <div class="text-gray-900">${this.formatHours(totalHours)}h total</div>
                <div class="text-green-600">${this.formatHours(approvedHours)}h approved</div>
                <div class="text-orange-500">${this.formatHours(pendingHours)}h pending</div>
                <div class="text-red-600">${this.formatHours(rejectedHours)}h rejected</div>
                <div class="text-blue-600">${this.formatHours(totalUnits)} units</div>
                <div class="text-gray-500 text-xs">${workingDays} days</div>
            </div>
        `;

        return td;
    },

    /**
     * Determines the status of a cell based on payroll data.
     */
    determineCellStatus(dayData) {
        const { total_hours, approved_hours, rejected_hours } = dayData;

        if (total_hours === 0) return 'no_data';
        if (approved_hours > 0) return 'approved';  // Show green if any hours are approved
        if (rejected_hours === total_hours) return 'rejected';
        return 'pending';
    },

    /**
     * Gets the CSS class for a given status.
     */
    getStatusClass(status) {
        const classes = {
            'approved': 'bg-green-100 text-green-800 border-green-200',
            'rejected': 'bg-red-100 text-red-800 border-red-200',
            'pending': 'bg-yellow-100 text-yellow-800 border-yellow-200',
            'no_data': 'bg-gray-50 text-gray-400 border-gray-200'
        };
        return classes[status] || classes['no_data'];
    },

    /**
     * Formats hours with conditional decimal places (max 2 decimal places).
     */
    formatHours(value) {
        if (value === undefined || value === null || value === 0) {
            return '0';
        }
        return parseFloat(value).toFixed(2).replace(/\.?0+$/, '');
    },

    /**
     * Shows or hides the empty state.
     */
    showEmptyState(show) {
        const emptyState = document.getElementById('empty-state');
        const table = document.querySelector('.overflow-x-auto');

        if (show) {
            emptyState?.classList.remove('hidden');
            table?.classList.add('hidden');
        } else {
            emptyState?.classList.add('hidden');
            table?.classList.remove('hidden');
        }
    },

    /**
     * Shows an error state.
     */
    showError() {
        this.showEmptyState(true);
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    PayrollTableManager.init();
    // Make PayrollTableManager globally available for tooltips
    window.PayrollTableManager = PayrollTableManager;
});
