// Payroll Tooltips Manager
const PayrollTooltips = {
    currentTooltip: null,
    tooltipContainer: null,

    /**
     * Initialize tooltip functionality.
     */
    init() {
        this.createTooltipContainer();
        this.attachTooltipListeners();
    },

    /**
     * Create a dedicated tooltip container for better positioning control.
     */
    createTooltipContainer() {
        this.tooltipContainer = document.createElement('div');
        this.tooltipContainer.id = 'payroll-tooltip-container';
        this.tooltipContainer.className = 'fixed inset-0 pointer-events-none z-[9999]';
        document.body.appendChild(this.tooltipContainer);
    },

    /**
     * Attach tooltip event listeners to date cells.
     */
    attachTooltipListeners() {
        // Use event delegation to handle dynamically created cells
        const tableBody = document.getElementById('payroll-table-body');

        if (!tableBody) {
            console.warn('Table body not found for tooltips');
            return;
        }

        // Add mouseover event listener for instant tooltip display
        tableBody.addEventListener('mouseover', (event) => {
            const cell = event.target.closest('td');
            if (cell && this.isDateCell(cell)) {
                clearTimeout(this.hideTimeout);
                this.showTooltip(cell);
            }
        });

        // Add mouseout event listener
        tableBody.addEventListener('mouseout', (event) => {
            const cell = event.target.closest('td');
            if (cell && this.isDateCell(cell)) {
                // Small delay to prevent flickering when moving between adjacent cells
                clearTimeout(this.hideTimeout);
                this.hideTimeout = setTimeout(() => {
                    this.hideTooltip();
                }, 100);
            }
        });

        // Prevent hiding when hovering over tooltip itself
        document.addEventListener('mouseover', (event) => {
            if (this.currentTooltip && this.currentTooltip.contains(event.target)) {
                clearTimeout(this.hideTimeout);
            }
        });
    },

    /**
     * Check if a cell is a date cell (not technician name or summary).
     */
    isDateCell(cell) {
        // Date cells don't have sticky positioning classes
        return !cell.classList.contains('sticky');
    },

    /**
     * Show tooltip above the cell.
     */
    showTooltip(cell) {
        // Hide any existing tooltip
        this.hideTooltip();

        // Get cell data
        const technician = this.getTechnicianFromRow(cell);
        const date = this.getDateFromCell(cell);
        const dayData = this.getDayData(technician, date);

        // Create tooltip
        const tooltip = this.createTooltip(date, dayData);

        // Position tooltip relative to the cell
        this.positionTooltip(tooltip, cell);

        // Store reference
        this.currentTooltip = tooltip;
    },

    /**
     * Hide the current tooltip.
     */
    hideTooltip() {
        clearTimeout(this.hideTimeout);

        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    },

    /**
     * Get technician name from the row.
     */
    getTechnicianFromRow(cell) {
        const row = cell.closest('tr');
        if (!row) return 'Unknown';

        const technicianCell = row.querySelector('td.sticky.left-0');
        return technicianCell ? technicianCell.textContent.trim() : 'Unknown';
    },

    /**
     * Get date from cell position.
     */
    getDateFromCell(cell) {
        const row = cell.closest('tr');
        const table = row.closest('table');
        const headerRow = table.querySelector('thead tr');

        if (!headerRow) {
            return null;
        }

        // Find cell index (excluding technician column)
        const cells = Array.from(row.children);
        const cellIndex = cells.indexOf(cell);

        // Get corresponding header (skip technician header)
        const headers = Array.from(headerRow.children);
        const dateHeader = headers[cellIndex];

        if (!dateHeader || cellIndex === 0 || cellIndex === cells.length - 1) {
            return null; // First is technician, last is summary
        }

        // Get date from PayrollTableManager dates array
        if (window.PayrollTableManager && window.PayrollTableManager.payrollData.dates) {
            const dateIndex = cellIndex - 1; // Subtract 1 for technician column
            const availableDates = window.PayrollTableManager.payrollData.dates;

            if (dateIndex >= 0 && dateIndex < availableDates.length) {
                return availableDates[dateIndex];
            }
        }

        return null;
    },

    /**
     * Get day data for a specific technician and date.
     */
    getDayData(technician, date) {
        if (!window.PayrollTableManager || !date) return null;

        const technicianData = PayrollTableManager.payrollData.data[technician];
        return technicianData ? technicianData[date] || null : null;
    },

    /**
     * Create tooltip element with dynamic sizing.
     */
    createTooltip(date, dayData) {
        // Create main tooltip container
        const tooltip = document.createElement('div');
        tooltip.className = 'absolute bg-gray-900 text-white text-sm rounded-lg shadow-2xl border border-gray-700 pointer-events-none';
        tooltip.style.maxWidth = '400px';
        tooltip.style.minWidth = '200px';

        // Create content container with proper padding (no scrolling)
        const contentContainer = document.createElement('div');
        contentContainer.className = 'p-4';

        // Format date for display
        const formattedDate = date ? this.formatDateHeader(new Date(date)) : 'Unknown Date';

        // Create content
        if (!dayData || dayData.total_hours === 0) {
            contentContainer.innerHTML = `
                <div class="font-semibold text-white mb-2 break-words">${this.escapeHtml(formattedDate)}</div>
                <div class="text-gray-400">No hours logged for the day</div>
            `;
        } else {
            // Header
            const header = document.createElement('div');
            header.className = 'font-semibold text-white mb-3 break-words';
            header.textContent = formattedDate;
            contentContainer.appendChild(header);

            // Activities section
            if (dayData.project_activities && dayData.project_activities.length > 0) {
                const activitiesSection = this.createProjectActivitiesSection(dayData.project_activities);
                contentContainer.appendChild(activitiesSection);
            } else if (dayData.activities && dayData.activities.length > 0) {
                const activitiesSection = this.createSimpleActivitiesSection(dayData.activities, dayData.sample_comments);
                contentContainer.appendChild(activitiesSection);
            }

            // Summary section
            const summarySection = this.createSummarySection(dayData);
            contentContainer.appendChild(summarySection);
        }

        // Add content to tooltip
        tooltip.appendChild(contentContainer);

        // Create arrow element
        const arrow = document.createElement('div');
        arrow.className = 'tooltip-arrow absolute w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-transparent border-t-gray-900';
        arrow.style.top = '100%';
        arrow.style.left = '50%';
        arrow.style.transform = 'translateX(-50%)';
        tooltip.appendChild(arrow);

        return tooltip;
    },

    /**
     * Create project activities section with proper DOM structure.
     */
    createProjectActivitiesSection(projectActivities) {
        if (!projectActivities || !projectActivities.length) return document.createElement('div');

        const section = document.createElement('div');
        section.className = 'mb-3';

        // Group activities by project
        const projectGroups = {};

        projectActivities.forEach(activity => {
            const project = activity.project_name || 'Unknown Project';
            const units = parseFloat(activity.units || 0);

            if (!projectGroups[project]) {
                projectGroups[project] = {
                    activities: [],
                    totalUnits: 0
                };
            }

            projectGroups[project].activities.push(activity);
            projectGroups[project].totalUnits += units;
        });

        Object.keys(projectGroups).forEach((project, index) => {
            const group = projectGroups[project];
            const unitsText = ` (${this.formatHours(group.totalUnits)} units)`;

            // Add spacing between projects (except first)
            if (index > 0) {
                const spacer = document.createElement('div');
                spacer.className = 'mb-2';
                section.appendChild(spacer);
            }

            // Project header
            const projectHeader = document.createElement('div');
            projectHeader.className = 'text-yellow-400 font-medium mb-1 break-words';
            projectHeader.textContent = `${project}${unitsText}:`;
            section.appendChild(projectHeader);

            // Activities list
            const activitiesList = document.createElement('div');
            activitiesList.className = 'ml-2 space-y-1';

            group.activities.forEach(activity => {
                const activityItem = document.createElement('div');
                activityItem.className = 'text-white text-xs break-words';

                const activityName = activity.activity_name || 'Unknown Activity';
                const timeRange = this.formatTimeRange(activity.hours_start, activity.hours_end);
                const comment = activity.comment;

                // Create activity text with proper styling
                const activitySpan = document.createElement('span');
                activitySpan.textContent = `• ${activityName}`;
                activityItem.appendChild(activitySpan);

                if (timeRange) {
                    const timeSpan = document.createElement('span');
                    timeSpan.className = 'text-gray-400 ml-1';
                    timeSpan.textContent = timeRange;
                    activityItem.appendChild(timeSpan);
                }

                if (comment && comment.trim() !== '' && comment !== 'null') {
                    const colonSpan = document.createElement('span');
                    colonSpan.textContent = ': ';
                    activityItem.appendChild(colonSpan);

                    const commentSpan = document.createElement('span');
                    commentSpan.className = 'text-cyan-400';
                    commentSpan.textContent = comment;
                    activityItem.appendChild(commentSpan);
                }

                activitiesList.appendChild(activityItem);
            });

            section.appendChild(activitiesList);
        });

        return section;
    },

    /**
     * Create simple activities section (fallback).
     */
    createSimpleActivitiesSection(activities, comments) {
        if (!activities || !activities.length) return document.createElement('div');

        const section = document.createElement('div');
        section.className = 'mb-3';

        // Activities header
        const header = document.createElement('div');
        header.className = 'text-blue-300 font-medium mb-1';
        header.textContent = 'Activities:';
        section.appendChild(header);

        // Activities list
        const activitiesList = document.createElement('div');
        activitiesList.className = 'ml-2 space-y-1';

        activities.forEach((activity, index) => {
            const activityItem = document.createElement('div');
            activityItem.className = 'text-gray-300 text-xs break-words';

            if (comments && comments.trim() !== '' && comments !== 'null' && index === 0) {
                activityItem.innerHTML = `• ${this.escapeHtml(activity)}: <span class="text-purple-300">${this.escapeHtml(comments)}</span>`;
            } else {
                activityItem.textContent = `• ${activity}`;
            }

            activitiesList.appendChild(activityItem);
        });

        section.appendChild(activitiesList);
        return section;
    },

    /**
     * Create summary section with hours breakdown.
     */
    createSummarySection(dayData) {
        const section = document.createElement('div');
        section.className = 'mt-3 pt-3 border-t border-gray-600 space-y-1';

        // Helper function to create summary row
        const createSummaryRow = (label, value, colorClass = 'text-white') => {
            const row = document.createElement('div');
            row.className = `${colorClass} text-xs`;
            row.innerHTML = `${this.escapeHtml(label)}: <span class="font-medium">${this.escapeHtml(value)}</span>`;
            return row;
        };

        // Add summary rows
        section.appendChild(createSummaryRow('Total', `${this.formatHours(dayData.total_hours)}h`));
        section.appendChild(createSummaryRow('Approved', `${this.formatHours(dayData.approved_hours)}h`, 'text-green-400'));
        section.appendChild(createSummaryRow('Pending', `${this.formatHours(dayData.pending_hours)}h`, 'text-orange-400'));
        section.appendChild(createSummaryRow('Rejected', `${this.formatHours(dayData.rejected_hours)}h`, 'text-red-400'));

        if (dayData.entry_count) {
            section.appendChild(createSummaryRow('Activities', dayData.entry_count.toString()));
        }

        // Always show total units, even if 0
        const totalUnits = dayData.total_units || 0;
        section.appendChild(createSummaryRow('Total units', this.formatHours(totalUnits)));

        if (dayData.approved_by) {
            section.appendChild(createSummaryRow('Approved by', dayData.approved_by, 'text-cyan-400'));
        }

        if (dayData.approved_at) {
            section.appendChild(createSummaryRow('Approved at', this.formatDateTime(dayData.approved_at), 'text-cyan-400'));
        }

        return section;
    },

    /**
     * Escape HTML to prevent XSS attacks.
     */
    escapeHtml(text) {
        if (typeof text !== 'string') return text;
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * Format time range for activities.
     */
    formatTimeRange(startTime, endTime) {
        if (!startTime || !endTime) return '';

        try {
            const start = new Date(startTime);
            const end = new Date(endTime);

            const startStr = start.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            const endStr = end.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });

            return `(${startStr}-${endStr})`;
        } catch (error) {
            return '';
        }
    },

    /**
     * Format date for tooltip header.
     */
    formatDateHeader(date) {
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    },

    /**
     * Format datetime for approval information.
     */
    formatDateTime(datetime) {
        if (!datetime) return '';
        try {
            const date = new Date(datetime);
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        } catch (error) {
            return datetime;
        }
    },

    /**
     * Position tooltip relative to the cell with smart viewport handling.
     */
    positionTooltip(tooltip, cell) {
        // Append to tooltip container
        this.tooltipContainer.appendChild(tooltip);

        // Get cell and viewport dimensions
        const cellRect = cell.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Set initial position off-screen to measure tooltip dimensions
        tooltip.style.position = 'absolute';
        tooltip.style.visibility = 'hidden';
        tooltip.style.left = '-9999px';
        tooltip.style.top = '-9999px';

        // Force layout and get accurate measurements after content is rendered
        tooltip.offsetHeight;
        tooltip.offsetWidth;
        const tooltipRect = tooltip.getBoundingClientRect();

        // Calculate preferred position (centered above cell)
        let left = cellRect.left + (cellRect.width / 2) - (tooltipRect.width / 2);
        let top = cellRect.top - tooltipRect.height - 12; // 12px gap for arrow

        // Determine if tooltip should be above or below based on available space
        const spaceAbove = cellRect.top;
        const spaceBelow = viewportHeight - cellRect.bottom;
        const tooltipHeight = tooltipRect.height;

        const showBelow = (spaceAbove < tooltipHeight + 20) || (spaceBelow > spaceAbove);

        if (showBelow) {
            top = cellRect.bottom + 12;
            this.updateArrowDirection(tooltip, 'up');
        } else {
            this.updateArrowDirection(tooltip, 'down');
        }

        // Handle horizontal overflow with better margin handling
        const margin = 15;
        const maxLeft = viewportWidth - tooltipRect.width - margin;

        if (left < margin) {
            left = margin;
        } else if (left > maxLeft) {
            left = maxLeft;
        }

        // Handle vertical overflow - ensure tooltip stays within viewport
        if (top < margin) {
            top = margin;
        } else if (top + tooltipRect.height > viewportHeight - margin) {
            top = Math.max(margin, viewportHeight - tooltipRect.height - margin);
        }

        // Apply final position
        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;
        tooltip.style.visibility = 'visible';

        // Update arrow position based on cell center
        this.updateArrowPosition(tooltip, cellRect, left);
    },

    /**
     * Update arrow direction based on tooltip position.
     */
    updateArrowDirection(tooltip, direction) {
        const arrow = tooltip.querySelector('.tooltip-arrow');
        if (!arrow) return;

        if (direction === 'up') {
            // Arrow pointing up (tooltip below cell)
            arrow.className = 'tooltip-arrow absolute w-0 h-0 border-l-[6px] border-r-[6px] border-b-[6px] border-transparent border-b-gray-900';
            arrow.style.top = '-6px';
            arrow.style.bottom = 'auto';
        } else {
            // Arrow pointing down (tooltip above cell)
            arrow.className = 'tooltip-arrow absolute w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-transparent border-t-gray-900';
            arrow.style.top = '100%';
            arrow.style.bottom = 'auto';
        }
    },

    /**
     * Update arrow horizontal position to point to cell center.
     */
    updateArrowPosition(tooltip, cellRect, tooltipLeft) {
        const arrow = tooltip.querySelector('.tooltip-arrow');
        if (!arrow) return;

        const cellCenter = cellRect.left + (cellRect.width / 2);
        const arrowLeft = cellCenter - tooltipLeft;
        const tooltipWidth = tooltip.offsetWidth;

        // Constrain arrow position within tooltip bounds
        const minArrowLeft = 12; // 12px from edge
        const maxArrowLeft = tooltipWidth - 12;
        const constrainedArrowLeft = Math.max(minArrowLeft, Math.min(maxArrowLeft, arrowLeft));

        arrow.style.left = `${constrainedArrowLeft}px`;
        arrow.style.transform = 'translateX(-50%)';
    },

    /**
     * Format date for display.
     */
    formatDate(date) {
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    /**
     * Format hours with max 2 decimal places.
     */
    formatHours(value) {
        if (value === undefined || value === null || value === 0) {
            return '0';
        }
        return parseFloat(value).toFixed(2).replace(/\.?0+$/, '');
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    PayrollTooltips.init();
});
